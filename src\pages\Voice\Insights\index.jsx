import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import DCard from '@/components/Global/DCard';
import DLoading from '@/components/DLoading';
import DCheckbox from '@/components/Global/DCheckbox';
import DSelect from '@/components/Global/DSelect';
import DButton from '@/components/Global/DButton';
import useModalStore from '@/stores/modal/modalStore';
import DDateRangePicker from '@/components/DDateRangePicker';
import InsightsChart from '@/pages/ChatbotDetails/InsightsChart';
import featureCheck from '@/helpers/tier/featureCheck';
import InlineGlobalModals from '@/components/InlineGlobalModals';
import * as voiceService from '@/services/voice.service';
import useDanteApi from '@/hooks/useDanteApi';
import { DateTime } from 'luxon';

const VoiceInsights = () => {
  const { id } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [voiceData, setVoiceData] = useState(null);
  const openPlansModal = useModalStore((state) => state.openPlansModal);
  
  // Chart visualization states
  const [showCalls, setShowCalls] = useState(true);
  const [showDuration, setShowDuration] = useState(true);
  const [showSuccess, setShowSuccess] = useState(true);
  const [showFailure, setShowFailure] = useState(true);
  const [dateSelectedGeneral, setDateSelectedGeneral] = useState('Last 7 days');
  const [timeRange, setTimeRange] = useState({
    date_from: DateTime.now().minus({ days: 7 }).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
    date_to: DateTime.now().toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
  });
  const [tempDateRange, setTempDateRange] = useState({
    from: null,
    to: null
  });
  const [exportInsights, setExportInsights] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const { data: voiceInsightsData, loading: insightsLoading } = useDanteApi(
    voiceService.getVoiceInsights, 
    [timeRange.date_from, timeRange.date_to], 
    {}, 
    timeRange.date_from, 
    timeRange.date_to
  );

  const filterOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: 'custom', label: 'Custom' }
  ];

  const handleDateRangeChange = (fromDate, toDate) => {
    if (fromDate && toDate) {
      setTimeRange({
        date_from: DateTime.fromISO(fromDate).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
        date_to: DateTime.fromISO(toDate).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
      });
    } else {
      setTempDateRange({
        from: fromDate,
        to: toDate
      });
    }
  };

  // Handle date filter change
  const handleDateFilterChange = (value) => {
    setDateSelectedGeneral(filterOptions.find(option => option.value === value)?.label || 'custom');
    
    if (value !== 'custom') {
      let fromDate;
      
      if (value === '7d') {
        fromDate = DateTime.now().minus({ days: 7 });
      } else if (value === '30d') {
        fromDate = DateTime.now().minus({ days: 30 });
      } else if (value === '90d') {
        fromDate = DateTime.now().minus({ days: 90 });
      }
      
      setTimeRange({
        date_from: fromDate.toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
        date_to: DateTime.now().toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
      });
      setTempDateRange({ from: null, to: null });
    }
  };

  // Format voiceInsightsData for InsightsChart
  useEffect(() => {
    if (voiceInsightsData) {
      setStatistics({
        credits: voiceInsightsData.credits_daily_usage || {},
        conversations: voiceInsightsData.conversations_daily_usage || {},
        messages: voiceInsightsData.messages_daily_usage || {},
        users: {}, // The backend doesn't provide users data currently
        nr_credits: voiceInsightsData.nr_credits || 0,
        nr_conversations: voiceInsightsData.nr_conversations || 0,
        nr_messages: voiceInsightsData.nr_messages || 0,
        nr_users: 0 // The backend doesn't provide this currently
      });
      
      setIsLoading(false);
    }
  }, [voiceInsightsData]);

  // Check feature access on mount
  useEffect(() => {
    if (featureCheck('ai_voice_insights', true)) {
      return;
    }
  }, []);

  if (isLoading || insightsLoading) {
    return <DLoading show={true} />;
  }

  return (
    <div className="flex flex-col gap-size5 p-size3 relative bg-white rounded-size1 h-full">
      <div className="flex flex-col gap-size2">
        <h1 className="text-xl font-medium tracking-tight">Voice Insights</h1>
        <p className="text-sm text-grey-50">Analytics and performance metrics for your AI Voice Agent</p>
      </div>
      
      <div className="border rounded-size1 border-grey-5 p-size5 flex flex-col gap-size3 overflow-y-auto ">
        <InlineGlobalModals />
        <div className="flex justify-between md:items-center flex-col gap-size2 lg:flex-row">
          <div className="flex gap-size4 w-full max-w-[100%] lg:max-w-[60%] xl:max-w-[70%] flex-wrap lg:flex-nowrap lg:overflow-x-auto lg:pb-size0">
            <div className="flex items-start p-size1 rounded-size0 bg-orange-5 w-[47%] lg:w-full">
              <DCheckbox
                checked={showCalls}
                style={{ width: 'auto' }}
                onChange={() => setShowCalls(!showCalls)}
              />
              <div className="flex flex-col w-full">
                <p className="text-lg font-medium tracking-tight">
                  {statistics?.nr_credits || 0}
                </p>
                <p className="text-xs text-grey-50 whitespace-nowrap">
                  Credits
                </p>
              </div>
            </div>
{/* 
            <div className="flex items-start p-size1 rounded-size0 bg-green-5 w-[47%] lg:w-full">
              <DCheckbox
                checked={showDuration}
                style={{ width: 'auto' }}
                onChange={() => setShowDuration(!showDuration)}
              />
              <div className="flex flex-col w-full">
                <p className="text-lg font-medium tracking-tight">
                  {statistics?.nr_users || 0}
                </p>
                <p className="text-xs text-grey-50 whitespace-nowrap">
                  Avg. Duration
                </p>
              </div>
            </div> */}

            <div className="flex items-start p-size1 rounded-size0 bg-purple-5 w-[47%] lg:w-full">
              <DCheckbox
                checked={showSuccess}
                style={{ width: 'auto' }}
                onChange={() => setShowSuccess(!showSuccess)}
              />
              <div className="flex flex-col w-full">
                <p className="text-lg font-medium tracking-tight">
                  {statistics?.nr_conversations || 0}
                </p>
                <p className="text-xs text-grey-50 whitespace-nowrap">
                  Conversations
                </p>
              </div>
            </div>

            <div className="flex items-start p-size1 rounded-size0 bg-negative-5 w-[47%] lg:w-full">
              <DCheckbox
                checked={showFailure}
                style={{ width: 'auto' }}
                onChange={() => setShowFailure(!showFailure)}
              />
              <div className="flex flex-col w-full">
                <p className="text-lg font-medium tracking-tight">
                  {statistics?.nr_messages || 0}
                </p>
                <p className="text-xs text-grey-50 whitespace-nowrap">
                  Messages
                </p>
              </div>
            </div>
          </div>
          <div className="flex gap-size1 w-full xl-max:flex-col-reverse xl-max:items-center xl-max:w-auto justify-end xl-max:justify-center">
            {dateSelectedGeneral?.toLowerCase() === 'custom' && (
              <DDateRangePicker
                fromDate={timeRange.date_from}
                toDate={timeRange.date_to}
                onApply={(from, to) => {
                  setTimeRange({
                    date_from: DateTime.fromISO(from).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS'),
                    date_to: DateTime.fromISO(to).toFormat('yyyy-MM-dd\'T\'HH:mm:ss.SSS')
                  });
                }}
              />
            )}
            <div className='flex gap-size1 w-full md:w-auto'>
              <DSelect
                listButtonClass="!h-[40px] !w-max !grow md:!grow-0"
                options={filterOptions}
                onChange={handleDateFilterChange}
                selectedChild={dateSelectedGeneral}
              />
              {/* <DButton variant="grey" className="!h-[40px] grow md:grow-0 !w-32" onClick={() => setExportInsights(true)}>
                <ExportIcon />
                <p className="text-base tracking-tight">Export</p>
              </DButton> */}
            </div>
          </div>
        </div>
        
        {statistics && (
          <InsightsChart
            apiResponse={isLoading ? {} : statistics}
            showConversations={showSuccess}
            showMessages={showFailure}
            showCredits={showCalls}
            showUniqueUsers={showDuration}
          />
        )}
      </div>
    </div>
  );
};

export default VoiceInsights; 